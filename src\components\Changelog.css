/* Changelog Modal Styles */
.changelog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: var(--space-lg);
}

.changelog-modal {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.changelog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-xl);
  border-bottom: 2px solid #f1f2f6;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.changelog-title h2 {
  margin: 0;
  font-size: var(--text-2xl);
  font-weight: 700;
}

.changelog-title p {
  margin: var(--space-xs) 0 0 0;
  opacity: 0.9;
  font-size: var(--text-sm);
}

.changelog-close {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  font-size: var(--text-xl);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  transition: background-color var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.changelog-close:hover {
  background: rgba(255, 255, 255, 0.3);
}

.changelog-filters {
  display: flex;
  gap: var(--space-sm);
  padding: var(--space-lg);
  border-bottom: 1px solid #e1e5e9;
  flex-wrap: wrap;
}

.filter-btn {
  background: #f8f9fa;
  border: 2px solid #e1e5e9;
  color: #495057;
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: var(--text-sm);
  font-weight: 500;
  white-space: nowrap;
}

.filter-btn:hover {
  background: #e9ecef;
  border-color: #ced4da;
}

.filter-btn.active {
  background: var(--color-primary);
  border-color: var(--color-primary);
  color: white;
}

.changelog-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-lg);
}

.changelog-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-2xl);
  color: #6c757d;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-md);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.changelog-empty {
  text-align: center;
  padding: var(--space-2xl);
  color: #6c757d;
}

.changelog-timeline {
  position: relative;
}

.changelog-entry {
  display: flex;
  margin-bottom: var(--space-xl);
  position: relative;
}

.entry-marker {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: var(--space-lg);
  position: relative;
}

.marker-dot {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-lg);
  color: white;
  font-weight: bold;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 2;
}

.marker-line {
  width: 2px;
  background: #e1e5e9;
  flex: 1;
  margin-top: var(--space-sm);
  min-height: 60px;
}

.entry-content {
  flex: 1;
  background: #f8f9fa;
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  border-left: 4px solid #e1e5e9;
}

.entry-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-md);
  flex-wrap: wrap;
  gap: var(--space-sm);
}

.entry-version {
  display: flex;
  gap: var(--space-sm);
  align-items: center;
  flex-wrap: wrap;
}

.version-badge {
  background: var(--color-dark);
  color: white;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
  font-weight: 600;
  letter-spacing: 0.5px;
}

.category-badge {
  color: white;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
  font-weight: 500;
}

.entry-date {
  color: #6c757d;
  font-size: var(--text-sm);
  font-weight: 500;
}

.entry-title {
  margin: 0 0 var(--space-sm) 0;
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--color-dark);
}

.entry-description {
  margin: 0 0 var(--space-md) 0;
  color: #495057;
  line-height: 1.6;
}

.entry-changes h4 {
  margin: 0 0 var(--space-sm) 0;
  font-size: var(--text-base);
  color: var(--color-dark);
}

.entry-changes ul {
  margin: 0;
  padding-left: var(--space-lg);
}

.entry-changes li {
  margin-bottom: var(--space-xs);
  color: #495057;
  line-height: 1.5;
}

.changelog-footer {
  padding: var(--space-lg);
  border-top: 1px solid #e1e5e9;
  background: #f8f9fa;
  text-align: center;
}

.changelog-footer p {
  margin: 0;
  color: #6c757d;
  font-size: var(--text-sm);
}

.changelog-footer p:first-child {
  margin-bottom: var(--space-sm);
  font-weight: 500;
}

.footer-note {
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
  .changelog-overlay {
    padding: var(--space-md);
  }

  .changelog-modal {
    max-height: 95vh;
  }

  .changelog-header {
    padding: var(--space-lg);
  }

  .changelog-title h2 {
    font-size: var(--text-xl);
  }

  .changelog-filters {
    padding: var(--space-md);
  }

  .filter-btn {
    font-size: var(--text-xs);
    padding: var(--space-xs) var(--space-sm);
  }

  .changelog-content {
    padding: var(--space-md);
  }

  .entry-marker {
    margin-right: var(--space-md);
  }

  .marker-dot {
    width: 32px;
    height: 32px;
    font-size: var(--text-base);
  }

  .entry-content {
    padding: var(--space-md);
  }

  .entry-header {
    flex-direction: column;
    align-items: flex-start;
  }
}

@media (max-width: 480px) {
  .changelog-filters {
    flex-direction: column;
  }

  .filter-btn {
    width: 100%;
    text-align: center;
  }

  .changelog-entry {
    flex-direction: column;
  }

  .entry-marker {
    flex-direction: row;
    margin-right: 0;
    margin-bottom: var(--space-md);
  }

  .marker-line {
    width: 100%;
    height: 2px;
    margin-top: 0;
    margin-left: var(--space-sm);
    min-height: auto;
  }
}

/* Dark mode support */
.dark-mode .changelog-modal {
  background: #2d3748;
  color: white;
}

.dark-mode .changelog-header {
  border-bottom-color: #4a5568;
}

.dark-mode .changelog-filters {
  border-bottom-color: #4a5568;
}

.dark-mode .filter-btn {
  background: #4a5568;
  border-color: #718096;
  color: #e2e8f0;
}

.dark-mode .filter-btn:hover {
  background: #718096;
}

.dark-mode .entry-content {
  background: #4a5568;
  border-left-color: #718096;
}

.dark-mode .changelog-footer {
  background: #4a5568;
  border-top-color: #718096;
}

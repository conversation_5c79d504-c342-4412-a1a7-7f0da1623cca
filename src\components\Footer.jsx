import React from 'react';
import { getFormattedVersion, getBuildEnvironment } from '../utils/version';
import './Footer.css';

const Footer = () => {
  const currentYear = new Date().getFullYear();
  const version = getFormattedVersion();
  const environment = getBuildEnvironment();

  return (
    <footer className="naroop-footer">
      <div className="footer-content">
        <div className="footer-section">
          <h4>NAROOP</h4>
          <p>Narrative of Our People</p>
          <p className="footer-mission">
            Uplifting Black voices through stories, community, and empowerment.
          </p>
        </div>

        <div className="footer-section">
          <h4>Community</h4>
          <ul className="footer-links">
            <li><a href="#stories">Stories</a></li>
            <li><a href="#economic">Economic Hub</a></li>
            <li><a href="#dialogue">Community Dialogue</a></li>
            <li><a href="#support">Support</a></li>
          </ul>
        </div>

        <div className="footer-section">
          <h4>Resources</h4>
          <ul className="footer-links">
            <li><a href="/kids">Kids Zone</a></li>
            <li><a href="#activism">Community Activism</a></li>
            <li><a href="#about">About Us</a></li>
            <li><a href="#contact">Contact</a></li>
          </ul>
        </div>

        <div className="footer-section">
          <h4>Connect</h4>
          <div className="social-links">
            <span>🌍 Building Community</span>
            <span>💪 Empowering Voices</span>
            <span>✊ Unity & Growth</span>
          </div>
        </div>
      </div>

      <div className="footer-bottom">
        <div className="footer-bottom-content">
          <div className="copyright">
            <p>&copy; {currentYear} NAROOP. All rights reserved.</p>
            <p className="footer-tagline">
              Celebrating Black excellence, one story at a time.
            </p>
          </div>
          
          <div className="version-info">
            <span className="version-badge">
              {version}
              {environment === 'development' && (
                <span className="dev-indicator">dev</span>
              )}
            </span>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;

import React, { useRef, useState } from 'react';
import { useAuth } from '../AuthContext';

export default function Login({ onSwitchToSignup, onSwitchToReset, onBackToLanding }) {
  const emailRef = useRef();
  const passwordRef = useRef();
  const { login } = useAuth();
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  async function handleSubmit(e) {
    e.preventDefault();
    setError('');
    setLoading(true);
    try {
      await login(emailRef.current.value, passwordRef.current.value);
    } catch (err) {
      setError('Failed to log in.');
    }
    setLoading(false);
  }

  return (
    <div className="auth-form">
      {onBackToLanding && (
        <button className="back-to-landing" onClick={onBackToLanding}>
          ← Back to Home
        </button>
      )}
      <h2>Log In</h2>
      {error && <div className="auth-error">{error}</div>}
      <form onSubmit={handleSubmit}>
        <input type="email" ref={emailRef} placeholder="Email" required />
        <input type="password" ref={passwordRef} placeholder="Password" required />
        <button disabled={loading} type="submit">Log In</button>
      </form>
      <p>Need an account? <button onClick={onSwitchToSignup}>Sign Up</button></p>
      <p><button onClick={onSwitchToReset}>Forgot Password?</button></p>
    </div>
  );
}

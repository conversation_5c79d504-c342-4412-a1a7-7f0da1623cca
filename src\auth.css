/* Auth form styles for NAROOP */
.auth-form {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 16px rgba(0,0,0,0.08);
  max-width: 350px;
  margin: 60px auto 0 auto;
  padding: 2rem 2rem 1.5rem 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.back-to-landing {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background: none;
  border: none;
  color: #6c757d;
  font-size: 0.9rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 6px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.back-to-landing:hover {
  background: #f8f9fa;
  color: #495057;
}
.auth-form h2 {
  color: #22223b;
  margin-bottom: 1.2rem;
}
.auth-form input {
  width: 100%;
  padding: 0.7rem 1rem;
  margin-bottom: 1rem;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
  background: #f8f8fa;
  transition: border 0.2s;
}
.auth-form input:focus {
  border: 1.5px solid #fbbf24;
  outline: none;
}
.auth-form button[type="submit"] {
  width: 100%;
  background: #22223b;
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 0.7rem 0;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  margin-bottom: 0.5rem;
  transition: background 0.2s;
}
.auth-form button[type="submit"]:hover {
  background: #fbbf24;
  color: #22223b;
}
.auth-form p {
  margin-top: 0.5rem;
  font-size: 0.95rem;
}
.auth-form .auth-error {
  color: #e63946;
  background: #fff0f3;
  border-radius: 8px;
  padding: 0.5rem 1rem;
  margin-bottom: 1rem;
  width: 100%;
  text-align: center;
}
.auth-form button {
  background: none;
  border: none;
  color: #3a86ff;
  cursor: pointer;
  font-size: 1rem;
  text-decoration: underline;
  padding: 0;
}
.auth-form button:focus {
  outline: 2px solid #fbbf24;
}

.naroop-auth-header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 1rem;
  padding: 1rem 2rem 0 2rem;
}
.naroop-logout-btn {
  background: #e63946;
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 0.5rem 1.2rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s;
}
.naroop-logout-btn:hover {
  background: #fbbf24;
  color: #22223b;
}

.naroop-account-link {
  background: #3a86ff;
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 0.5rem 1.2rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s;
  text-decoration: none;
  display: inline-block;
}

.naroop-account-link:hover {
  background: #2563eb;
  color: #fff;
}

/* Account Page Styles */
.account-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f7fafc 0%, #fbbf24 100%);
  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.account-header {
  background: linear-gradient(90deg, #222 0%, #e63946 60%, #fbbf24 100%);
  color: white;
  padding: 1.5rem 2rem;
  box-shadow: 0 4px 16px rgba(26,26,46,0.08);
}

.account-header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.account-header h1 {
  margin: 0;
  font-size: 2rem;
  font-weight: 700;
}

.back-to-home-btn, .logout-btn {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  padding: 0.5rem 1rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
}

.back-to-home-btn:hover, .logout-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

.account-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.profile-section {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(251, 191, 36, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f8f9fa;
}

.section-header h2 {
  margin: 0;
  color: #22223b;
  font-size: 1.5rem;
  font-weight: 600;
}

.edit-profile-btn {
  background: #3a86ff;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.edit-profile-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

/* Profile Display Styles */
.profile-display {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.profile-avatar-section {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.profile-avatar {
  font-size: 4rem;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  border-radius: 50%;
  box-shadow: 0 4px 15px rgba(251, 191, 36, 0.3);
}

.profile-basic-info h3 {
  margin: 0 0 0.5rem 0;
  color: #22223b;
  font-size: 1.8rem;
  font-weight: 700;
}

.profile-email {
  color: #6b7280;
  margin: 0 0 0.25rem 0;
  font-size: 1rem;
}

.profile-join-date {
  color: #9ca3af;
  margin: 0;
  font-size: 0.9rem;
}

.profile-bio {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  border-left: 4px solid #fbbf24;
}

.profile-bio h4 {
  margin: 0 0 0.75rem 0;
  color: #22223b;
  font-size: 1.1rem;
  font-weight: 600;
}

.profile-bio p {
  margin: 0;
  color: #4b5563;
  line-height: 1.6;
}

/* Profile Edit Form Styles */
.profile-edit-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  font-weight: 600;
  color: #22223b;
  font-size: 0.9rem;
}

.form-group input,
.form-group textarea {
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  background: #f9fafb;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #fbbf24;
  background: white;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.save-btn, .cancel-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.save-btn {
  background: #10b981;
  color: white;
}

.save-btn:hover {
  background: #059669;
  transform: translateY(-1px);
}

.cancel-btn {
  background: #6b7280;
  color: white;
}

.cancel-btn:hover {
  background: #4b5563;
}

/* Community Profile Styles */
.community-profile-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.community-field {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.25rem;
  border-left: 4px solid #e63946;
}

.community-field h4 {
  margin: 0 0 0.75rem 0;
  color: #22223b;
  font-size: 1rem;
  font-weight: 600;
}

.community-field p {
  margin: 0;
  color: #4b5563;
  line-height: 1.5;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tag {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: #22223b;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
}

.mentor-status {
  font-weight: 600;
}

.mentor-status.available {
  color: #10b981;
}

.mentor-status.unavailable {
  color: #6b7280;
}

/* Statistics Section Styles */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 16px;
  padding: 1.5rem;
  text-align: center;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
  transition: transform 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  z-index: 1;
}

.stat-card > * {
  position: relative;
  z-index: 2;
}

.stat-card:hover {
  transform: translateY(-4px);
}

.stat-icon {
  font-size: 2rem;
  margin-bottom: 0.75rem;
  display: block;
  background: rgba(255, 255, 255, 0.2);
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 0.75rem auto;
  backdrop-filter: blur(10px);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 900;
  margin-bottom: 0.5rem;
  display: block;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  color: #ffffff;
}

.stat-label {
  font-size: 0.9rem;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  color: #ffffff;
  opacity: 0.95;
}

/* Badges Section */
.badges-section {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  border-left: 4px solid #fbbf24;
}

.badges-section h4 {
  margin: 0 0 1rem 0;
  color: #22223b;
  font-size: 1.1rem;
  font-weight: 600;
}

.badges-list {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.badge {
  background: white;
  border-radius: 12px;
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 2px solid #fbbf24;
}

.badge-icon {
  font-size: 1.5rem;
}

.badge-name {
  font-weight: 600;
  color: #22223b;
}

/* Settings Section */
.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.setting-item {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.25rem;
  border-left: 4px solid #3a86ff;
}

.setting-item h4 {
  margin: 0 0 0.5rem 0;
  color: #22223b;
  font-size: 1rem;
  font-weight: 600;
}

.setting-item p {
  margin: 0 0 0.5rem 0;
  color: #4b5563;
  font-weight: 500;
}

.setting-item small {
  color: #9ca3af;
  font-size: 0.85rem;
}

/* Loading and Error States */
.account-loading, .account-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  text-align: center;
  padding: 2rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #fbbf24;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.account-error h2 {
  color: #e63946;
  margin-bottom: 1rem;
}

.account-error button {
  background: #e63946;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.3s ease;
}

.account-error button:hover {
  background: #dc2626;
}

/* Responsive Design */
@media (max-width: 768px) {
  .account-header-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .account-header h1 {
    font-size: 1.5rem;
  }

  .account-content {
    padding: 1rem;
  }

  .profile-avatar-section {
    flex-direction: column;
    text-align: center;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .form-actions {
    justify-content: center;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .community-profile-grid,
  .settings-grid {
    grid-template-columns: 1fr;
  }

  .badges-list {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .back-to-home-btn, .logout-btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.9rem;
  }
}
